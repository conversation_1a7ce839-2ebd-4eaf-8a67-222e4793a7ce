#!/usr/bin/env python3
"""
Test script to demonstrate the enhanced JSON error handling functionality.
This script tests the fix_json_with_llm function and parse_and_validate_llm_response function.
"""

import json
import sys
import os

# Add the ai_core module to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'apps', 'ai_core', 'src'))

from ai_core.answer_questions_langchain_knowledgebase_subgrouping import (
    fix_json_with_llm, 
    parse_and_validate_llm_response
)

def test_json_error_handling():
    """Test the JSON error handling functionality"""
    
    # Test cases with various JSON errors
    test_cases = [
        {
            "name": "Missing closing brace",
            "input": '{"question_code": "M1000", "question_text": "Test question", "answer_text": ["Test answer"]',
            "expected_error": "Expecting ',' delimiter"
        },
        {
            "name": "Extra comma",
            "input": '{"question_code": "M1000", "question_text": "Test question", "answer_text": ["Test answer"],}',
            "expected_error": "Expecting property name"
        },
        {
            "name": "Unescaped quotes",
            "input": '{"question_code": "M1000", "question_text": "Patient said "I feel fine"", "answer_text": ["Test answer"]}',
            "expected_error": "Invalid control character"
        },
        {
            "name": "Missing quotes around property",
            "input": '{question_code: "M1000", "question_text": "Test question", "answer_text": ["Test answer"]}',
            "expected_error": "Expecting property name"
        }
    ]
    
    # Mock OpenAI API key (you would need a real one for actual testing)
    openai_api_key = "your-openai-api-key-here"
    
    print("🧪 Testing JSON Error Handling Functionality")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test Case {i}: {test_case['name']}")
        print(f"Input: {test_case['input']}")
        
        # First, verify that the input actually causes a JSON error
        try:
            json.loads(test_case['input'])
            print("❌ ERROR: Test input should have caused a JSON error but didn't!")
            continue
        except json.JSONDecodeError as e:
            print(f"✅ Confirmed JSON error: {e}")
        
        # Test the fix_json_with_llm function (requires real API key)
        if openai_api_key != "your-openai-api-key-here":
            print("🔧 Testing LLM JSON fixing...")
            try:
                fixed_json = fix_json_with_llm(test_case['input'], str(e), openai_api_key)
                
                # Try to parse the fixed JSON
                try:
                    parsed_fixed = json.loads(fixed_json)
                    print("✅ LLM successfully fixed the JSON!")
                    print(f"Fixed JSON: {fixed_json}")
                except json.JSONDecodeError as fix_error:
                    print(f"❌ LLM fix failed: {fix_error}")
                    
            except Exception as llm_error:
                print(f"❌ Error during LLM fixing: {llm_error}")
        else:
            print("⚠️ Skipping LLM test (no API key provided)")
        
        # Test the parse_and_validate_llm_response function
        print("🔍 Testing parse_and_validate_llm_response...")
        question_answer_list = []
        
        try:
            result = parse_and_validate_llm_response(
                test_case['input'], 
                question_answer_list, 
                openai_api_key if openai_api_key != "your-openai-api-key-here" else None
            )
            
            if result:
                print("✅ parse_and_validate_llm_response succeeded!")
                print(f"Parsed items: {len(question_answer_list)}")
            else:
                print("❌ parse_and_validate_llm_response returned False")
                
        except Exception as parse_error:
            print(f"❌ parse_and_validate_llm_response failed: {parse_error}")
    
    print("\n" + "=" * 60)
    print("🏁 JSON Error Handling Test Complete")


def test_valid_json():
    """Test that valid JSON still works correctly"""
    print("\n🧪 Testing Valid JSON Handling")
    print("=" * 40)
    
    valid_json = '''[
        {
            "question_code": "M1000",
            "question_text": "Test question",
            "question_type": "radio-group",
            "answer_context": ["Patient said they feel fine"],
            "answer_reason": ["Based on patient statement"],
            "answer_text": ["0 - Independent"],
            "confidence_score": 0.95
        }
    ]'''
    
    question_answer_list = []
    
    try:
        result = parse_and_validate_llm_response(valid_json, question_answer_list)
        
        if result:
            print("✅ Valid JSON processed successfully!")
            print(f"Parsed items: {len(question_answer_list)}")
            print(f"First item: {question_answer_list[0] if question_answer_list else 'None'}")
        else:
            print("❌ Valid JSON processing failed")
            
    except Exception as e:
        print(f"❌ Error processing valid JSON: {e}")


if __name__ == "__main__":
    print("🚀 Starting JSON Error Handling Tests")
    
    # Test invalid JSON handling
    test_json_error_handling()
    
    # Test valid JSON handling
    test_valid_json()
    
    print("\n✨ All tests completed!")
    print("\n📝 Note: To test LLM fixing functionality, you need to:")
    print("   1. Set a valid OpenAI API key in the script")
    print("   2. Ensure you have OpenAI credits available")
    print("   3. The LLM will attempt to fix malformed JSON automatically")
