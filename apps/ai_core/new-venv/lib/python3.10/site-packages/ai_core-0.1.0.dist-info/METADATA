Metadata-Version: 2.4
Name: ai_core
Version: 0.1.0
Summary: A FastAPI application in a monorepo
Author-email: <PERSON><PERSON><PERSON> <<EMAIL>>
Requires-Python: >=3.10
Requires-Dist: GitPython
Requires-Dist: deepgram_sdk
Requires-Dist: fastapi
Requires-Dist: pydantic-settings
Requires-Dist: jsonschema
Requires-Dist: openai==1.65.1
Requires-Dist: psutil
Requires-Dist: pydantic
Requires-Dist: pydantic-settings
Requires-Dist: pytest
Requires-Dist: python-dotenv
Requires-Dist: python-multipart
Requires-Dist: pyjwt
Requires-Dist: pyyaml
Requires-Dist: requests
Requires-Dist: slack_sdk
Requires-Dist: uvicorn
Requires-Dist: pymongo
Requires-Dist: langchain
Requires-Dist: langchain-openai
Requires-Dist: langchain-community
Requires-Dist: langchain-text-splitters
Requires-Dist: chromadb
Requires-Dist: tiktoken
Provides-Extra: dev
Requires-Dist: pytest; extra == "dev"
